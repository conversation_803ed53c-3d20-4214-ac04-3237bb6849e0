"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { useEffect, useState } from "react";
import HistogramChart from "./components/histogram-chart";
import { Button } from "@/src/components/ui/button";
import { useRouter } from "next/navigation";

export default function Histogram() {
    const [loading, setLoading] = useState(true);
    const router = useRouter();

    useEffect(() => {
        setLoading(false);
    }, [])

    return (
        <ContentWrapper title="Histograma" loading={loading}>
            <div className="flex items-center mb-6">
                <Button
                    variant="ghost"
                    className="flex items-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
                    onClick={() => router.push("/views/crm/proposals/accepted")}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Voltar</span>
                </Button>
                <div className="h-0.5 flex-grow bg-gray-100 ml-4"></div>
            </div>
            <HistogramChart />
        </ContentWrapper>
    )
}