"use client";
import { findProposal } from "@/src/actions/proposals";
import { updateProposalWorkTotalCost } from "@/src/actions/update-proposal-work-total-cost";
import {
	loadRepairBudgets,
	removeRepairBudget,
} from "@/src/actions/repair-budget";
import { loadServicesScope } from "@/src/actions/services-scopes";
import ContentWrapper from "@/src/components/content-wrapper";
import { Button } from "@/src/components/ui/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
} from "@/src/components/ui/dialog";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { success, toast } from "@/src/hooks/use-toast";
import { formatCurrency } from "@/src/lib/utils";
import { Proposal } from "@/src/types/core/proposal";
import { RepairBudget } from "@/src/types/core/repair-budget";
import { ServicesScope } from "@/src/types/core/services-scope";
import {
	DialogDescription,
	DialogTitle,
	DialogTrigger,
} from "@radix-ui/react-dialog";
import { Edit, Trash } from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import RepairBudgetForm from "./components/repair-budget-form";
import AppConfirmationDialog from "@/src/components/app-confirmation-dialog";
import { useRouter } from "next/navigation";
import { useIsMobile } from "@/src/hooks/use-mobile";
import { CurrencyInput } from "@/src/components/currency-input";

export default function ProposalServicesBudget() {
	const searchParams = useSearchParams();
	const router = useRouter();
	const isMobile = useIsMobile();

	const [proposal, setProposal] = useState<Proposal | null>(null);
	const [repairBudgets, setRepairBudgets] = useState<RepairBudget[]>([]);
	const [services, setServices] = useState<ServicesScope[]>([]);
	const [repairBudget, setRepairBudget] = useState<RepairBudget | undefined>(
		undefined
	);
	const [loading, setLoading] = useState(true);
	const [open, setOpen] = useState(false);
	const [customerName, setCustomerName] = useState("");
	const [proposalName, setProposalName] = useState("");
	const [isConfirmationOpen, setIsConfirmationOpen] = useState(false);
	const [selectedBudgetId, setSelectedBudgetId] = useState<string>();
	const [obraValue, setObraValue] = useState<number>(0);
	const [isUpdateValueConfirmationOpen, setIsUpdateValueConfirmationOpen] = useState(false);
	// const [obraRawValue, setObraRawValue] = useState("");
	const labelClassName =
		"text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 font-bold text-gray-700";

	// Função utilitária para formatar valor monetário enquanto digita
	// function formatMoneyInput(value: string) {
	// 	const onlyDigits = value.replace(/\D/g, "");
	// 	const number = parseFloat(onlyDigits) / 100;
	// 	return number.toLocaleString("pt-BR", { style: "currency", currency: "BRL" });
	// }

	const fetchProposal = async (proposalId: string) => {
		try {
			setLoading(true);
			const data = await findProposal(proposalId);
			if (data) {
				setProposal(data);
				setCustomerName(data.customer?.name || "");
				setProposalName(data.name);
				setObraValue(data.workTotalCost || 0);
			}
		} catch (error) {
			console.error(error);
		} finally {
			setLoading(false);
		}
	};

	const fetchServices = async () => {
		try {
			setLoading(true);
			const data = await loadServicesScope(["REPAIR_SERVICE"]);
			setServices((data?.data || []) as ServicesScope[]);

		} catch (error) {
			console.error("Erro ao carregar serviços:", error);
		} finally {
			setLoading(false);
		}
	};

	const fetchRepairBudgets = async (proposalId: string) => {
		try {
			setLoading(true);
			const data = await loadRepairBudgets(proposalId);
			if (data) {
				setRepairBudgets(data);
			}
		} catch (error) {
			console.error("Erro ao carregar orçamentos de reparo:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleDeleteClick = (budgetId: string) => {
		setSelectedBudgetId(budgetId);
		setIsConfirmationOpen(true);
	};

	const handleConfirmDelete = () => {
		if (selectedBudgetId) {
			deleteRepairBudget(selectedBudgetId);
			setIsConfirmationOpen(false);
			setSelectedBudgetId('');
		}
	};

	const handleUpdateValueConfirm = async () => {
		try {
			setLoading(true);
			const result = await updateProposalWorkTotalCost(proposal!.id, obraValue);
			if (result.success) {
				await fetchProposal(proposal!.id); // Recarrega a proposta após atualizar
				toast(success("Valor da obra atualizado com sucesso!"));
			} else {
				toast({ title: "Erro ao atualizar valor da obra", description: String(result.error), variant: "destructive" });
			}
		} catch (error) {
			toast({ title: "Erro ao atualizar valor da obra", description: String(error), variant: "destructive" });
		} finally {
			setLoading(false);
			setIsUpdateValueConfirmationOpen(false);
		}
	};

	const deleteRepairBudget = async (id: string) => {
		try {
			setLoading(true);
			const data = await removeRepairBudget(id);
			if (data) {
				toast(success(data.message));
				setRepairBudgets(repairBudgets.filter((budget) => budget.id !== id));
			}
		} catch (error) {
			console.error(error);
		} finally {
			setLoading(false);
		}
	};

	const getPriorityClasses = (igrf: number) => {
		if (igrf < 4)
			return "text-base font-semibold px-3 py-1.5 rounded-md bg-green-50 text-green-700 border border-green-200 shadow-sm";
		if (igrf >= 4 && igrf <= 7)
			return "text-base font-semibold px-3 py-1.5 rounded-md bg-yellow-50 text-yellow-700 border border-yellow-200 shadow-sm";
		return "text-base font-semibold px-3 py-1.5 rounded-md bg-red-50 text-red-700 border border-red-200 shadow-sm";
	};

	const getPriorityLabel = (igrf: number) => {
		if (igrf < 4) return "Baixa";
		if (igrf >= 4 && igrf <= 7) return "Média";
		return "Alta";
	};

	useEffect(() => {
		fetchServices();
	}, []);

	useEffect(() => {
		const id = searchParams.get("id");
		if (id) {
			fetchProposal(id);
			fetchRepairBudgets(id);
		}
	}, [searchParams.get("id")]);

	return (
		<ContentWrapper title="Acompanhamento de Obra" loading={loading}>
			<div className="flex items-center mb-6">
				<Button
					variant="ghost"
					className="flex items-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
					onClick={() => router.push("/views/crm/proposals/accepted")}
				>
					<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
						<path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
					</svg>
					<span className="font-medium">Voltar</span>
				</Button>
				<div className="h-0.5 flex-grow bg-gray-100 ml-4"></div>
			</div>
			<div className="flex flex-col gap-3 mt-4">
				<div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-3'} gap-4`}>
					<div>
						<Label className={labelClassName}>Proposta</Label>
						<Input disabled={true} value={proposalName} />
					</div>
					<div>
						<Label className={labelClassName}>Cliente</Label>
						<Input disabled={true} value={customerName} />
					</div>
					<div>
						<Label className={labelClassName}>Custo Total da Obra</Label>
						<div className="flex gap-2">
							<div className="flex-1">
								<CurrencyInput
									value={obraValue}
									onChange={setObraValue}
									name="obraValue"
									placeholder="Digite o valor da obra"
								/>
							</div>
							{proposal && (
								<Button
									type="button"
									className="bg-amber-500 hover:bg-amber-600 text-white px-3 py-2 h-10 whitespace-nowrap"
									onClick={() => setIsUpdateValueConfirmationOpen(true)}
								>
									Atualizar
								</Button>
							)}
						</div>
					</div>
				</div>
			</div>
			<Dialog
				open={open}
				onOpenChange={(open) => {
					setOpen(open);
					setRepairBudget(undefined);
				}}
			>
				<div className={`${isMobile ? 'flex flex-col' : 'flex justify-end'} mt-4 gap-4`}>
					<DialogTrigger asChild>
						{proposal && (
							<Button className={`bg-green-500 hover:bg-green-600 ${isMobile ? 'w-full' : ''}`} type="button">
								Adicionar Atividade
							</Button>
						)}
					</DialogTrigger>
				</div>
				<DialogContent className="max-w-7xl w-[90vw] pt-10 pb-6">
					<DialogHeader>
						<DialogTitle className="font-bold text-2xl">
							Etapa / Atividade
						</DialogTitle>
						<DialogDescription />
					</DialogHeader>
					<div className="py-2">
						<RepairBudgetForm
							proposal={proposal!}
							services={services}
							repairBudget={repairBudget}
							onCancelClick={(formChanged) => {
								setOpen(false);
								setRepairBudget(undefined);
								// Só busca os dados novamente se o formulário foi alterado
								if (formChanged) {
									fetchRepairBudgets(proposal!.id);
								}
							}}
						/>
					</div>
				</DialogContent>
			</Dialog>

			{/* Dialog de confirmação para atualizar valor da obra */}
			<AppConfirmationDialog
				title="Atualizar Valor da Obra"
				description={`Tem certeza que deseja atualizar o valor da obra ?
					Os indicadores serão recalculados.`}
				onConfirmCallback={handleUpdateValueConfirm}
				open={isUpdateValueConfirmationOpen}
				onOpenChange={setIsUpdateValueConfirmationOpen}
				confirmButtonText="Atualizar"
			>
				<div />
			</AppConfirmationDialog>

			<div className="mt-6 flex flex-col gap-6">
				{!!repairBudgets.length ? (
					repairBudgets.map((budget, index) => (
						<div
							key={index}
							className="p-4 lg:p-5 rounded-lg border bg-white border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 hover:border-gray-300"
						>
							{/* Nome do serviço - sempre em cima, independente do tamanho da tela */}
							<div className="flex items-center justify-start mb-4">
								<h1 className="text-lg lg:text-xl text-green-700 font-bold bg-green-50 px-3 lg:px-4 py-1.5 lg:py-2 rounded-lg border border-green-100 shadow-sm">
									{budget.serviceScope.name}
								</h1>
							</div>

							{/* Grid para informações principais */}
							<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-4 lg:gap-4">
								{/* Coluna 1: Valor do serviço */}
								<div className="flex flex-col lg:col-span-4">
									<h1 className="text-sm text-gray-500 font-medium mb-1">Valor do serviço</h1>
									<span className="text-base font-semibold px-3 py-1.5 rounded-md bg-green-50 text-green-700 border border-green-200 shadow-sm w-full text-center">
										{formatCurrency(budget.serviceCost)}
									</span>
								</div>

								{/* Coluna 2: Indicadores */}
								<div className="flex flex-col lg:col-span-4">
									<h1 className="text-sm text-gray-500 font-medium mb-1">Indicadores</h1>
									<div className="grid grid-cols-3 gap-1 w-full">
										<div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] lg:h-[40px] justify-between">
											<div className="text-indigo-700 font-medium text-xs text-center">G x U x T</div>
											<div className="text-indigo-800 font-semibold text-center">{budget.gut}</div>
										</div>
										<div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] lg:h-[40px] justify-between">
											<div className="text-indigo-700 font-medium text-xs text-center">IGRF</div>
											<div className="text-indigo-800 font-semibold text-center">{Number(budget.igrf).toFixed(2)}</div>
										</div>
										<div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] lg:h-[40px] justify-between">
											<div className="text-indigo-700 font-medium text-xs text-center">IMP</div>
											<div className="text-indigo-800 font-semibold text-center">{Number((budget as any).imp || 0).toFixed(2)}</div>
										</div>
									</div>
								</div>

								{/* Coluna 3: Prioridade */}
								<div className="flex flex-col lg:col-span-2">
									<h1 className="text-sm text-gray-500 font-medium mb-1">Prioridade</h1>
									<span className={getPriorityClasses(Number(budget.igrf))}>
										{getPriorityLabel(Number(budget.igrf))}
									</span>
								</div>

								{/* Coluna 4: Ações */}
								<div className="flex flex-col lg:col-span-2">
									<h1 className="text-sm text-gray-500 font-medium mb-1">Ações</h1>
									<div className="flex gap-2 h-[40px]">
										<button
											className="flex-1 rounded-md bg-green-50 hover:bg-green-100 transition-colors duration-200 flex items-center justify-center gap-2"
											onClick={() => {
												setRepairBudget(budget);
												setOpen(true);
											}}
											title="Editar atividade"
										>
											<Edit className="text-green-600" size={18} />
											<span className="text-green-700 font-medium text-sm">Editar</span>
										</button>
										<AppConfirmationDialog
											title={`Deletar o serviço: ${budget.serviceScope.name}`}
											description='Tem certeza que deseja deletar este serviço? A ação não poderá ser desfeita!'
											onConfirmCallback={handleConfirmDelete}
											open={isConfirmationOpen}
											onOpenChange={setIsConfirmationOpen}
										>
											<button
												className="flex-1 rounded-md bg-red-50 hover:bg-red-100 transition-colors duration-200 flex items-center justify-center gap-2"
												onClick={() => handleDeleteClick(budget.id)}
												title="Excluir atividade"
											>
												<Trash className="text-red-600" size={18} />
												<span className="text-red-700 font-medium text-sm">Excluir</span>
											</button>
										</AppConfirmationDialog>
									</div>
								</div>
							</div>
						</div>
					))
				) : (
					<div className="h-40 flex items-center justify-center text-center text-gray-500 font-semibold bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
						<div className="p-6"> Não há atividades cadastradas. Clique em &quot;Adicionar Atividade&quot; para começar. </div>
					</div>
				)}
			</div>
		</ContentWrapper>
	);
}
