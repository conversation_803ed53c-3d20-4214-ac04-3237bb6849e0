"use client"
import ContentWrapper from "@/src/components/content-wrapper";
import { useEffect, useState } from "react";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Proposal } from "@/src/types/core/proposal";
import { useRouter, useSearchParams } from "next/navigation";
import { findProposal } from "@/src/actions/proposals";
import { DialogHeader } from "@/src/components/ui/dialog";
import { Dialog, DialogContent, DialogTitle, DialogDescription } from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import RepairBudgetForm from "../../services-budget/components/repair-budget-form";
import { RepairBudget } from "@/src/types/core/repair-budget";
import { Ruler, Calendar } from "lucide-react";
import { CustomInput } from "@/src/components/app-input";
import { Form<PERSON>rovider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ProposalPlannningSchema, proposalPlannningSchema } from "../schemas/proposal-planning.schema";
import ProductivityData from "../components/productivity-data";
import { loadServicesScope } from "@/src/actions/services-scopes";
import { ServicesScope } from "@/src/types/core/services-scope";
import { formatCurrency } from "@/src/lib/utils";
import { findRepairBudgetByPeriod, loadHistograms } from "@/src/actions/histogram";
import { loadRepairBudgets } from "@/src/actions/repair-budget";
import { useIsMobile } from "@/src/hooks/use-mobile";

export default function HistogramService() {
    const isMobile = useIsMobile();

    const getPriorityClasses = (igrf: number) => {
        if (igrf < 4)
            return "text-sm font-semibold px-3 py-1.5 rounded-md bg-green-50 text-green-700 border border-green-200 shadow-sm h-[40px] flex items-center justify-center";
        if (igrf >= 4 && igrf <= 7)
            return "text-sm font-semibold px-3 py-1.5 rounded-md bg-yellow-50 text-yellow-700 border border-yellow-200 shadow-sm h-[40px] flex items-center justify-center";
        return "text-sm font-semibold px-3 py-1.5 rounded-md bg-red-50 text-red-700 border border-red-200 shadow-sm h-[40px] flex items-center justify-center";
    };

    const getPriorityLabel = (igrf: number) => {
        if (igrf < 4) return "Baixa";
        if (igrf >= 4 && igrf <= 7) return "Média";
        return "Alta";
    };

    // Função auxiliar para verificar se a medição pertence ao período selecionado
    const isSamePeriod = (productivity: any, selectedPeriod: any) => {
        if (!productivity || !selectedPeriod) return false;

        console.log('CRITICAL FIX: Checking if periods match:');
        console.log('- Productivity:', productivity);
        console.log('- Selected period:', selectedPeriod);

        // Verificar se productivity é um array e usar o primeiro item
        const productivityData = Array.isArray(productivity) ? productivity[0] : productivity;

        if (!productivityData) {
            console.log('CRITICAL FIX: No productivity data to check');
            return false;
        }

        // Verificar se os IDs são iguais (caso simples)
        if (productivityData.periodicity?.id === selectedPeriod.id) {
            console.log('CRITICAL FIX: Periods match by ID');
            return true;
        }

        // Verificar se os conteúdos são iguais (formato estruturado)
        if (productivityData.periodicity?.content === selectedPeriod.content) {
            console.log('CRITICAL FIX: Periods match by content');
            return true;
        }

        // Verificar se o período selecionado está no formato mês-ano (MM-YYYY)
        if (selectedPeriod.id && selectedPeriod.id.includes('-') && /^\d{1,2}-\d{4}$/.test(selectedPeriod.id)) {
            const [month, year] = selectedPeriod.id.split('-').map(Number);
            const expectedContent = `month=${month};year=${year}`;

            console.log(`CRITICAL FIX: Checking month-year format: ${month}-${year}`);
            console.log(`CRITICAL FIX: Expected content: ${expectedContent}`);
            console.log(`CRITICAL FIX: Actual content: ${productivityData.periodicity?.content}`);

            // Verificar se o conteúdo da medição corresponde ao formato esperado
            if (productivityData.periodicity?.content === expectedContent) {
                console.log('CRITICAL FIX: Periods match by expected content');
                return true;
            }

            // Verificar se o conteúdo da medição contém informações de mês e ano que correspondem
            if (productivityData.periodicity?.content &&
                productivityData.periodicity.content.includes(`month=${month}`) &&
                productivityData.periodicity.content.includes(`year=${year}`)) {
                console.log('CRITICAL FIX: Periods match by month and year in content');
                return true;
            }
        }

        console.log('CRITICAL FIX: Periods do not match');
        return false;
    };
    const searchParams = useSearchParams();
    const [proposal, setProposal] = useState<Proposal | null>(null);
    const [loading, setLoading] = useState(true);
    const [customerName, setCustomerName] = useState("");
    const [proposalName, setProposalName] = useState("");
    const [open, setOpen] = useState(false);
    const [openProductivity, setOpenProductivity] = useState(false);
    const [plannings, setPlannings] = useState<any[]>([]);
    const [filteredServices, setFilteredServices] = useState<any[]>([]);
    const [periodSelected, setPeriodSelected] = useState<any>(null);
    const [services, setServices] = useState<ServicesScope[]>([]);
    const [repairBudgetHistogram, setRepairBudgetHistogram] = useState<(RepairBudget & { productivity?: any[] }) | undefined>(
        undefined
    );
    const [defaultPeriod, setDefaultPeriod] = useState<string>('')
    const [monthYearPeriods, setMonthYearPeriods] = useState<{ id: string, label: string, content: string, order: number }[]>([])

    const router = useRouter();

    const methods = useForm<ProposalPlannningSchema>({
        resolver: zodResolver(proposalPlannningSchema),
    });

    const { watch, setValue } = methods;
    const selectedPeriodId = watch('period');

    // Função para gerar períodos de mês/ano entre duas datas
    const generateMonthYearPeriods = (repairBudgets: any[]) => {
        const periods: { id: string, label: string, content: string, order: number }[] = [];
        const monthsSet = new Set<string>();

        repairBudgets.forEach(budget => {
            if (budget.startDate && budget.endDate) {
                const startDate = new Date(budget.startDate);
                const endDate = new Date(budget.endDate);

                const currentDate = new Date(startDate);

                while (currentDate <= endDate) {
                    // IMPORTANTE: getMonth() retorna 0-11 (janeiro=0, fevereiro=1, etc.)
                    // Precisamos ajustar para 1-12 para o ID e content
                    const displayMonth = currentDate.getMonth(); // 0-11
                    const month = displayMonth + 1; // 1-12
                    const year = currentDate.getFullYear();

                    console.log(`CRITICAL FIX: Current date: ${currentDate.toISOString()}`);
                    console.log(`CRITICAL FIX: Display month (0-11): ${displayMonth}, Real month (1-12): ${month}, year: ${year}`);

                    // Formatar o mês em português usando o displayMonth (0-11)
                    const monthNames = [
                        'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
                        'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
                    ];

                    // Usar o formato estruturado para o content e o ID para compatibilidade
                    // IMPORTANTE: Usar o mês real (1-12) para o ID e content
                    const periodId = `${month}-${year}`;
                    const periodContent = `month=${month};year=${year}`;
                    const periodLabel = `${monthNames[displayMonth]} / ${year}`;

                    console.log(`CRITICAL FIX: Generated period - ID: ${periodId}, Content: ${periodContent}, Label: ${periodLabel}`);

                    if (!monthsSet.has(periodId)) {
                        monthsSet.add(periodId);
                        periods.push({
                            id: periodId, // Manter o ID no formato MM-YYYY para compatibilidade
                            content: periodContent, // Adicionar o content no formato estruturado
                            label: periodLabel,
                            order: periods.length // Usar o índice como order para garantir que seja único
                        });
                    }

                    // Avançar para o próximo mês
                    currentDate.setMonth(currentDate.getMonth() + 1);
                }
            }
        });

        // Ordenar períodos por data (mais recente primeiro)
        return periods.sort((a, b) => {
            const [monthA, yearA] = a.id.split('-').map(Number);
            const [monthB, yearB] = b.id.split('-').map(Number);

            if (yearA !== yearB) return yearA - yearB;
            return monthA - monthB;
        });
    };

    // Função para filtrar serviços por mês/ano
    const filterServicesByMonthYear = (monthYearId: string, services: any[]) => {
        if (!monthYearId || !services.length) return [];

        // IMPORTANTE: O mês no ID é 1-12 (janeiro=1, fevereiro=2, etc.)
        // Precisamos ajustar para 0-11 para usar no Date
        const [monthStr, yearStr] = monthYearId.split('-');
        const month = parseInt(monthStr, 10); // 1-12
        const displayMonth = month - 1; // 0-11
        const year = parseInt(yearStr, 10);

        console.log(`CRITICAL FIX: Filtering services for month-year: ${monthStr}-${yearStr}`);
        console.log(`CRITICAL FIX: Real month (1-12): ${month}, Display month (0-11): ${displayMonth}, year: ${year}`);

        return services.filter(service => {
            if (!service.startDate || !service.endDate) return false;

            const startDate = new Date(service.startDate);
            const endDate = new Date(service.endDate);

            // Verificar se o mês/ano selecionado está dentro do período do serviço
            // IMPORTANTE: Usar o displayMonth (0-11) para criar as datas
            const periodDate = new Date(year, displayMonth);
            const periodEndDate = new Date(year, displayMonth + 1, 0); // Último dia do mês

            // IMPORTANTE: getMonth() retorna 0-11, então precisamos comparar com displayMonth
            return (
                (startDate <= periodEndDate && endDate >= periodDate) ||
                (startDate.getMonth() === displayMonth && startDate.getFullYear() === year) ||
                (endDate.getMonth() === displayMonth && endDate.getFullYear() === year)
            );
        });
    };

    const fetchProposal = async (proposalId: string) => {
        try {
            setLoading(true);
            const data = await findProposal(proposalId);
            if (data) {
                setProposal(data);
                setCustomerName(data.customer?.name || "");
                setProposalName(data.name);
                setPlannings(Array.isArray(data.plannings) ? data.plannings : []);
                if (Array.isArray(data.plannings) && data.plannings.length > 0) {
                    setValue('period', data.plannings[0].id);
                    findServiceByPeriod(data.plannings[0].id);
                    setDefaultPeriod(data.plannings[0].id);
                    setPeriodSelected(data.plannings[0]);
                } else {
                    setValue('period', '');
                    setDefaultPeriod('');
                    setPeriodSelected(null);
                }
            }
        } catch (error) {
            console.error("Erro ao carregar proposta:", error);
        } finally {
            setLoading(false);
        }
    };

    const fetchRepairBudgets = async (proposalId: string, preserveSelectedPeriod: boolean = false): Promise<void> => {
        // CRITICAL FIX: Retornar uma Promise para que possamos usar then/catch
        return new Promise<void>(async (resolve, reject) => {
            try {
                setLoading(true);
                console.log('CRITICAL FIX: Fetching repair budgets for proposal:', proposalId);

                const data = await loadHistograms(proposalId);
                const currentSelectedPeriodId = selectedPeriodId || plannings[0]?.id; // Usa o período selecionado atual

                if (data) {
                    setRepairBudgetHistogram(data);

                    // Carregar todos os orçamentos de reparo para gerar os períodos de mês/ano
                    const repairBudgets = await loadRepairBudgets(proposalId);
                    if (repairBudgets && repairBudgets.length > 0) {
                        const periods = generateMonthYearPeriods(repairBudgets);
                        setMonthYearPeriods(periods);

                        if (preserveSelectedPeriod && currentSelectedPeriodId) {
                            // Verificar se o período atual ainda existe nos períodos disponíveis
                            const periodExists = periods.some(p => p.id === currentSelectedPeriodId) ||
                                plannings.some(p => p.id === currentSelectedPeriodId);

                            if (periodExists) {
                                // Manter o período selecionado
                                if (currentSelectedPeriodId.includes('-')) {
                                    // É um período de mês/ano
                                    const filteredByMonth = filterServicesByMonthYear(currentSelectedPeriodId, repairBudgets);
                                    setFilteredServices(filteredByMonth);

                                    // CRITICAL FIX: Buscar dados de produtividade para cada orçamento filtrado
                                    try {
                                        // Criar um array de promessas para buscar dados de produtividade para cada orçamento
                                        const promises = filteredByMonth.map(budget => {
                                            // Construir a URL com os parâmetros
                                            const apiUrl = `/api/productivity?proposalId=${proposalId || ''}&repairBudgetId=${budget.id}&periodId=${currentSelectedPeriodId}`;
                                            console.log('CRITICAL FIX: API URL for budget', budget.id, ':', apiUrl);

                                            // Fazer a requisição
                                            return fetch(apiUrl)
                                                .then(response => {
                                                    if (!response.ok) {
                                                        throw new Error(`API returned ${response.status}`);
                                                    }
                                                    return response.json();
                                                })
                                                .then(data => {
                                                    console.log('CRITICAL FIX: API response data for budget', budget.id, ':', data);
                                                    return {
                                                        budgetId: budget.id,
                                                        data: data
                                                    };
                                                })
                                                .catch(error => {
                                                    console.error('CRITICAL FIX: Error fetching productivity data for budget', budget.id, ':', error);
                                                    return {
                                                        budgetId: budget.id,
                                                        error: error
                                                    };
                                                });
                                        });

                                        // Executar todas as promessas
                                        const results = await Promise.all(promises);
                                        console.log('CRITICAL FIX: All API calls completed:', results);

                                        // Atualizar os orçamentos filtrados com os dados de produtividade
                                        const updatedBudgets = filteredByMonth.map(budget => {
                                            // Encontrar o resultado para este orçamento
                                            const result = results.find(r => r.budgetId === budget.id);

                                            if (result && 'data' in result && result.data) {
                                                // Mesclar os dados do orçamento com os dados de produtividade
                                                return {
                                                    ...budget,
                                                    productivity: result.data.productivity || [],
                                                    allProductivities: result.data.allProductivities || []
                                                };
                                            }

                                            return budget;
                                        });

                                        // Atualizar a lista de orçamentos filtrados
                                        setFilteredServices(updatedBudgets);
                                    } catch (error) {
                                        console.error('CRITICAL FIX: Error fetching productivity data:', error);
                                    }
                                } else {
                                    // É um período normal
                                    await findServiceByPeriod(currentSelectedPeriodId);
                                }
                            } else {
                                // Se o período não existir mais, selecionar o primeiro disponível
                                if (periods.length > 0) {
                                    setValue('period', periods[0].id);
                                    const filteredByMonth = filterServicesByMonthYear(periods[0].id, repairBudgets);
                                    setFilteredServices(filteredByMonth);
                                }
                            }
                        } else {
                            // Comportamento padrão: selecionar o primeiro período
                            if (periods.length > 0) {
                                setValue('period', periods[0].id);
                                const filteredByMonth = filterServicesByMonthYear(periods[0].id, repairBudgets);
                                setFilteredServices(filteredByMonth);
                            }
                        }
                    }
                }

                // CRITICAL FIX: Resolver a Promise quando tudo estiver concluído
                console.log('CRITICAL FIX: Fetch repair budgets completed successfully');
                setLoading(false);
                resolve();
            } catch (error) {
                console.error('CRITICAL FIX: Error in fetchRepairBudgets:', error);
                setLoading(false);
                reject(error);
            }
        });
    };

    const fetchServices = async () => {
        try {
            setLoading(true);
            const data = await loadServicesScope(["REPAIR_SERVICE"]);
            setServices((data?.data || []) as ServicesScope[]);
        } catch (error) {
            console.error("Erro ao carregar serviços:", error);
        } finally {
            setLoading(false);
        }
    };

    const findServiceByPeriod = async (periodId: string) => {
        if (!periodId) {
            console.error('periodId is undefined or null');
            return;
        }

        try {
            setLoading(true);

            // Verificar se proposal existe
            if (!proposal || !proposal.id) {
                console.error('proposal is undefined or null');
                return;
            }

            const data = await findRepairBudgetByPeriod(proposal.id, periodId);
            if (data) {
                setFilteredServices(data);

                // Encontrar o período selecionado
                const selected = plannings.find((p) => p.id === periodId);
                if (selected) {
                    setPeriodSelected(selected);
                } else {
                    // Criar um objeto periodSelected com os valores mínimos necessários
                    setPeriodSelected({
                        id: periodId,
                        label: periodId, // Usar o ID como label se não encontrar o período
                        order: 0
                    });
                }
            }
        } catch (error) {
            console.error('Error in findServiceByPeriod:', error);
        } finally {
            setLoading(false);
        }
    }

    const handlePlanningChange = async (selectedPeriodId: any) => {
        console.log('CRITICAL FIX: Period changed to:', selectedPeriodId);

        // Verificar se é um ID de período no formato mês-ano
        if (selectedPeriodId && typeof selectedPeriodId === 'string' && selectedPeriodId.includes('-')) {
            // Carregar todos os orçamentos de reparo
            const repairBudgets = await loadRepairBudgets(proposal?.id || '');
            if (repairBudgets) {
                // Filtrar serviços pelo mês/ano selecionado
                const filteredByMonth = filterServicesByMonthYear(selectedPeriodId, repairBudgets);
                setFilteredServices(filteredByMonth);

                // Encontrar o período selecionado nos períodos de mês/ano
                const period = monthYearPeriods.find(p => p.id === selectedPeriodId);
                if (period) {
                    // Garantir que o objeto periodSelected tenha todas as propriedades necessárias
                    setPeriodSelected({
                        ...period,
                        content: period.content, // Incluir o content no formato estruturado
                        order: period.order || 0 // Garantir que order exista
                    });
                } else {
                    // Se não encontrar o período, criar um objeto com valores padrão
                    // Extrair mês e ano do ID
                    const [monthStr, yearStr] = selectedPeriodId.split('-');
                    // IMPORTANTE: O mês no ID é 1-12 (janeiro=1, fevereiro=2, etc.)
                    // Vamos armazenar exatamente esse valor no content
                    const month = parseInt(monthStr, 10); // Mês real (1-12)
                    const displayMonth = month - 1; // Mês ajustado (0-11) para usar no Date
                    const year = parseInt(yearStr, 10);

                    console.log(`CRITICAL FIX: Original month-year: ${monthStr}-${yearStr}`);
                    console.log(`CRITICAL FIX: Storing month=${month}, year=${year}`);
                    console.log(`CRITICAL FIX: Display month (0-11): ${displayMonth}`);

                    // Criar o content no formato estruturado
                    const content = `month=${month};year=${year}`;

                    // Formatar o label
                    const monthNames = [
                        'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
                        'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
                    ];
                    const label = `${monthNames[displayMonth]} / ${year}`;

                    setPeriodSelected({
                        id: selectedPeriodId,
                        content: content,
                        label: label,
                        order: 0
                    });
                }

                // CRITICAL FIX: Para cada orçamento filtrado, buscar dados de produtividade do novo endpoint
                console.log('CRITICAL FIX: Fetching productivity data for all filtered budgets');

                // Mostrar loading
                setLoading(true);

                // Criar um array de promessas para buscar dados de produtividade para cada orçamento
                const promises = filteredByMonth.map(budget => {
                    // Construir a URL com os parâmetros
                    const apiUrl = `/api/productivity?proposalId=${proposal?.id || ''}&repairBudgetId=${budget.id}&periodId=${selectedPeriodId}`;
                    console.log('CRITICAL FIX: API URL for budget', budget.id, ':', apiUrl);

                    // Fazer a requisição
                    return fetch(apiUrl)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`API returned ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('CRITICAL FIX: API response data for budget', budget.id, ':', data);
                            return {
                                budgetId: budget.id,
                                data: data
                            };
                        })
                        .catch(error => {
                            console.error('CRITICAL FIX: Error fetching productivity data for budget', budget.id, ':', error);
                            return {
                                budgetId: budget.id,
                                error: error
                            };
                        });
                });

                // Executar todas as promessas
                Promise.all(promises)
                    .then(results => {
                        console.log('CRITICAL FIX: All API calls completed:', results);

                        // Atualizar os orçamentos filtrados com os dados de produtividade
                        const updatedBudgets = filteredByMonth.map(budget => {
                            // Encontrar o resultado para este orçamento
                            const result = results.find(r => r.budgetId === budget.id);

                            if (result && 'data' in result && result.data) {
                                // Mesclar os dados do orçamento com os dados de produtividade
                                return {
                                    ...budget,
                                    productivity: result.data.productivity || [],
                                    allProductivities: result.data.allProductivities || []
                                };
                            }

                            return budget;
                        });

                        // Atualizar a lista de orçamentos filtrados
                        setFilteredServices(updatedBudgets);
                    })
                    .finally(() => {
                        setLoading(false);
                    });
            }
        } else {
            // Comportamento original para IDs de período que não são mês-ano
            findServiceByPeriod(selectedPeriodId);
        }
    };

    useEffect(() => {
        const id = searchParams.get("id");
        if (id) {
            fetchProposal(id)
            fetchRepairBudgets(id, false); // Não preservar o período selecionado na inicialização
        }
    }, [searchParams.get("id")]);

    useEffect(() => {
        fetchServices();
    }, []);

    useEffect(() => {
        if (selectedPeriodId) {
            handlePlanningChange(selectedPeriodId);
            const period = plannings.find((item) => item.id === selectedPeriodId)
            setPeriodSelected(period);
        }
    }, [selectedPeriodId]);

    // Removido o useEffect que recarregava os serviços quando repairBudgetHistogram mudava
    // Isso estava causando a perda da lista de atividades ao abrir o modal de medição

    return (
        <ContentWrapper title="Serviços para medição" loading={loading}>
            <div className="flex items-center mb-6">
                <Button
                    variant="ghost"
                    className="flex items-center text-blue-600 hover:text-blue-800 hover:bg-blue-50 transition-all duration-200"
                    onClick={() => router.push("/views/crm/proposals/accepted")}
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                    </svg>
                    <span className="font-medium">Voltar</span>
                </Button>
                <div className="h-0.5 flex-grow bg-gray-100 ml-4"></div>
            </div>
            <div className="flex flex-col gap-3 mt-4">
                <FormProvider {...methods}>
                    <div className={`grid ${isMobile ? 'grid-cols-1' : 'grid-cols-3'} gap-4`}>
                        <div>
                            <Label >Proposta</Label>
                            <Input disabled={true} value={proposalName} />
                        </div>
                        <div>
                            <Label >Cliente</Label>
                            <Input disabled={true} value={customerName} />
                        </div>
                        <div>
                            <CustomInput
                                label="Período"
                                name="period"
                                placeholder="Período"
                                type="select"
                                defaultValue={defaultPeriod}
                                onChange={(e) => {
                                    console.log('CRITICAL FIX: Period combobox changed to:', e.target.value);
                                    // Chamar a função handlePlanningChange diretamente
                                    handlePlanningChange(e.target.value);
                                }}
                                items={monthYearPeriods.length > 0 ?
                                    monthYearPeriods.map((item) => ({
                                        value: item.id,
                                        label: item.label,
                                    })) :
                                    plannings.map((item) => ({
                                        value: item.id,
                                        label: item.label,
                                    }))}
                            />
                        </div>
                    </div>
                </FormProvider>
            </div>
            <Dialog
                open={open}
                onOpenChange={(open) => {
                    setOpen(open);
                    setRepairBudgetHistogram(undefined);
                }}
            >
                <div className={`${isMobile ? 'flex flex-col' : 'flex justify-end'} mt-4 gap-4`}>
                    <Button
                        className={`bg-blue-500 hover:bg-blue-600 ${isMobile ? 'w-full' : ''}`}
                        onClick={() => proposal && router.push(`/views/crm/proposals/histogram?id=${proposal.id}`)}
                    >
                        Visualizar Histograma
                    </Button>
                    <Button
                        className={`bg-green-500 hover:bg-green-600 ${isMobile ? 'w-full' : ''}`}
                        type="button"
                        onClick={() => setOpen(true)}
                    >
                        Adicionar Atividade
                    </Button>
                </div>
                <DialogContent className="max-w-7xl w-[90vw] pt-10 pb-6">
                    <DialogHeader>
                        <DialogTitle className="font-bold text-2xl">
                            Etapa / Atividade
                        </DialogTitle>
                        <DialogDescription />
                    </DialogHeader>
                    <div className="py-2">
                        <RepairBudgetForm
                            proposal={proposal!}
                            services={services}
                            onCancelClick={(formChanged) => {
                                setOpen(false);
                                setRepairBudgetHistogram(undefined);
                                // Só busca os dados novamente se o formulário foi alterado
                                if (formChanged) {
                                    fetchRepairBudgets(proposal!.id, true); // Preservar o período selecionado
                                }
                            }}
                            onSave={() => {
                                // A chamada fetchRepairBudgets já é feita no onCancelClick quando formChanged=true
                            }}
                        />
                    </div>
                </DialogContent>
            </Dialog>
            {/* Usar um componente separado para o modal de produtividade para garantir que ele seja completamente desmontado */}
            {openProductivity ? (
                <Dialog
                    open={true}
                    onOpenChange={(open) => {
                        if (!open) {
                            console.log('CRITICAL FIX: Closing productivity modal');

                            // Fechar o modal imediatamente
                            setOpenProductivity(false);

                            // Limpar o orçamento selecionado imediatamente para evitar que dados persistam
                            setRepairBudgetHistogram(undefined);
                        }
                    }}
                >
                    <DialogContent className="max-w-7xl w-[90vw] pt-10 pb-6">
                        <DialogHeader>
                            <DialogTitle className="font-bold text-2xl">
                                {repairBudgetHistogram && isSamePeriod(repairBudgetHistogram.productivity, periodSelected) ? "Editar Medição" : "Realizar Medição"}
                            </DialogTitle>
                            <DialogDescription />
                        </DialogHeader>
                        <div className="py-2">
                            <ProductivityData
                                key={`productivity-${periodSelected.id}-${repairBudgetHistogram?.id}`} // CRITICAL FIX: Adicionar key para forçar remontagem do componente
                                proposal={proposal!}
                                services={services}
                                repairBudget={repairBudgetHistogram}
                                periodSelected={periodSelected}
                                onCancelClick={() => {
                                    console.log('CRITICAL FIX: Cancel button clicked in productivity modal');

                                    // Fechar o modal sem limpar a lista de atividades
                                    setOpenProductivity(false);

                                    // Limpar o orçamento selecionado imediatamente
                                    setRepairBudgetHistogram(undefined);
                                }}
                                closedAndFetch={() => {
                                    console.log('CRITICAL FIX: Closing modal and fetching data');

                                    // CRITICAL FIX: Buscar os dados atualizados antes de fechar o modal
                                    // para garantir que os dados sejam exibidos corretamente
                                    fetchRepairBudgets(proposal!.id, true).then(() => {
                                        console.log('CRITICAL FIX: Data fetched, now closing modal');

                                        // Fechar o modal após buscar os dados
                                        setOpenProductivity(false);

                                        // Limpar o orçamento selecionado após fechar o modal
                                        setRepairBudgetHistogram(undefined);
                                    }).catch(error => {
                                        console.error('CRITICAL FIX: Error fetching data:', error);

                                        // Fechar o modal mesmo em caso de erro
                                        setOpenProductivity(false);

                                        // Limpar o orçamento selecionado
                                        setRepairBudgetHistogram(undefined);

                                        // Tentar buscar os dados novamente
                                        fetchRepairBudgets(proposal!.id, true);
                                    });
                                }} />
                        </div>
                    </DialogContent>
                </Dialog>
            ) : null}

            <div className="mt-6 flex flex-col gap-6">
                {!!filteredServices.length ? (
                    filteredServices.map((budget, index) => (
                        <div
                            key={index}
                            className="p-4 lg:p-5 rounded-lg border bg-white border-gray-200 shadow-md hover:shadow-lg transition-all duration-300 hover:border-gray-300"
                        >
                            {/* Nome do serviço - sempre em cima, independente do tamanho da tela */}
                            <div className="flex items-center justify-start mb-4">
                                <h1 className="text-lg lg:text-xl text-green-700 font-bold bg-green-50 px-3 lg:px-4 py-1.5 lg:py-2 rounded-lg border border-green-100 shadow-sm">
                                    {budget.serviceScope.name}
                                </h1>
                            </div>

                            {/* Grid para informações principais - 6 colunas em desktop com a última coluna menor, empilhadas em mobile */}
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-4 lg:gap-4">
                                {/* Coluna 1: Valor do serviço */}
                                <div className="flex flex-col lg:col-span-2">
                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Valor do serviço</h1>
                                    <span className="text-sm font-semibold px-3 py-1.5 rounded-md bg-green-50 text-green-700 border border-green-200 shadow-sm w-full text-center h-[40px] flex items-center justify-center">
                                        {formatCurrency(budget.serviceCost)}
                                    </span>
                                </div>

                                {/* Coluna 2: Indicadores */}
                                <div className="flex flex-col lg:col-span-2">
                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Indicadores</h1>
                                    <div className="grid grid-cols-3 gap-1 w-full">
                                        <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] justify-between">
                                            <div className="text-indigo-700 font-medium text-xs text-center">G x U x T</div>
                                            <div className="text-indigo-800 font-semibold text-center">{budget.gut}</div>
                                        </div>
                                        <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] justify-between">
                                            <div className="text-indigo-700 font-medium text-xs text-center">IGRF</div>
                                            <div className="text-indigo-800 font-semibold text-center">{Number(budget.igrf).toFixed(2)}</div>
                                        </div>
                                        <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-indigo-50 border border-indigo-200 shadow-sm flex flex-col h-[40px] justify-between">
                                            <div className="text-indigo-700 font-medium text-xs text-center">IMP</div>
                                            <div className="text-indigo-800 font-semibold text-center">{Number((budget as any).imp || 0).toFixed(2)}</div>
                                        </div>
                                    </div>
                                </div>

                                {/* Coluna 3: Prioridade */}
                                <div className="flex flex-col lg:col-span-1">
                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Prioridade</h1>
                                    <span className={getPriorityClasses(Number(budget.igrf))}>
                                        {getPriorityLabel(Number(budget.igrf))}
                                    </span>
                                </div>

                                {/* Coluna 4: Evolução */}
                                <div className="flex flex-col lg:col-span-3">
                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Evolução</h1>
                                    {budget.productivity && budget.productivity.length > 0 && budget.productivity[0]?.startDate &&
                                        isSamePeriod(budget.productivity[0], periodSelected) &&
                                        (budget.productivity[0]?.predictedPeriodPercentage !== undefined || budget.productivity[0]?.realPeriodPercentage !== undefined) ? (
                                        <div className="grid grid-cols-3 gap-1 w-full">
                                            {budget?.buildingPercentage !== undefined ? (
                                                <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-blue-50 border border-blue-200 shadow-sm flex flex-col h-[40px] justify-between">
                                                    <div className="text-blue-700 font-medium text-xs text-center">Total</div>
                                                    <div className="text-blue-800 font-semibold text-center">{Number(budget.buildingPercentage).toFixed(2)}%</div>
                                                </div>
                                            ) : <div></div>}
                                            {budget.productivity[0]?.predictedPeriodPercentage !== undefined ? (
                                                <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-blue-50 border border-blue-200 shadow-sm flex flex-col h-[40px] justify-between">
                                                    <div className="text-blue-700 font-medium text-xs text-center">Previsto</div>
                                                    <div className="text-blue-800 font-semibold text-center">{Number(budget.productivity[0].predictedPeriodPercentage).toFixed(2)}%</div>
                                                </div>
                                            ) : <div></div>}
                                            {budget.productivity[0]?.realPeriodPercentage !== undefined ? (
                                                <div className="text-sm px-1 pt-1 pb-1 rounded-md bg-green-50 border border-green-200 shadow-sm flex flex-col h-[40px] justify-between">
                                                    <div className="text-green-700 font-medium text-xs text-center">Realizado</div>
                                                    <div className="text-green-800 font-semibold text-center">{Number(budget.productivity[0].realPeriodPercentage).toFixed(2)}%</div>
                                                </div>
                                            ) : <div></div>}
                                        </div>
                                    ) : (
                                        <div className="text-sm px-1 rounded-md bg-gray-50 border border-gray-200 shadow-sm flex items-center justify-center h-[40px]">
                                            <div className="text-gray-600 font-semibold text-center">Sem dados</div>
                                        </div>
                                    )}
                                </div>

                                {/* Coluna 5: Informação de medição */}
                                <div className="flex flex-col lg:col-span-2">
                                    {/* CRITICAL FIX: Verificar se há medição para o período selecionado */}
                                    {(() => {
                                        // Encontrar a medição para o período selecionado
                                        const productivityForSelectedPeriod = budget.productivity && budget.productivity.length > 0
                                            ? budget.productivity.find((p: any) =>
                                                (p.isSelectedPeriod || isSamePeriod(p, periodSelected)) && p.startDate
                                            )
                                            : null;

                                        // Encontrar outras medições
                                        const otherProductivities = budget.allProductivities && budget.allProductivities.length > 0
                                            ? budget.allProductivities
                                                .filter((p: any) =>
                                                    !(p.isSelectedPeriod || isSamePeriod(p, periodSelected)) &&
                                                    p.startDate &&
                                                    p.periodicity
                                                )
                                                // Ordenar por período (do mais antigo para o mais recente)
                                                .sort((a: any, b: any) => {
                                                    // Tentar extrair informações de mês e ano do conteúdo do período
                                                    const getMonthYearFromPeriodicity = (p: any) => {
                                                        if (!p.periodicity) return { month: 0, year: 0 };

                                                        // Verificar se temos um formato de conteúdo estruturado (month=X;year=Y)
                                                        if (p.periodicity.content && typeof p.periodicity.content === 'string') {
                                                            const monthMatch = p.periodicity.content.match(/month=(\d+)/);
                                                            const yearMatch = p.periodicity.content.match(/year=(\d+)/);

                                                            if (monthMatch && yearMatch) {
                                                                return {
                                                                    month: parseInt(monthMatch[1], 10),
                                                                    year: parseInt(yearMatch[1], 10)
                                                                };
                                                            }
                                                        }

                                                        // Verificar se temos um ID no formato MM-YYYY
                                                        if (p.periodicity.id && typeof p.periodicity.id === 'string' && p.periodicity.id.includes('-')) {
                                                            const [monthStr, yearStr] = p.periodicity.id.split('-');
                                                            if (!isNaN(parseInt(monthStr, 10)) && !isNaN(parseInt(yearStr, 10))) {
                                                                return {
                                                                    month: parseInt(monthStr, 10),
                                                                    year: parseInt(yearStr, 10)
                                                                };
                                                            }
                                                        }

                                                        // Verificar se temos uma ordem numérica
                                                        if (p.periodicity.order !== undefined && !isNaN(p.periodicity.order)) {
                                                            return { order: p.periodicity.order };
                                                        }

                                                        // Fallback: usar a data de início da medição
                                                        return { date: new Date(p.startDate).getTime() };
                                                    };

                                                    const aInfo = getMonthYearFromPeriodicity(a);
                                                    const bInfo = getMonthYearFromPeriodicity(b);

                                                    // Comparar por ano e mês se disponíveis
                                                    if (aInfo.year !== undefined && bInfo.year !== undefined) {
                                                        if (aInfo.year !== bInfo.year) {
                                                            return aInfo.year - bInfo.year; // Ordenar por ano (crescente)
                                                        }
                                                        if (aInfo.month !== undefined && bInfo.month !== undefined) {
                                                            return aInfo.month - bInfo.month; // Ordenar por mês (crescente)
                                                        }
                                                    }

                                                    // Comparar por ordem se disponível
                                                    if (aInfo.order !== undefined && bInfo.order !== undefined) {
                                                        return aInfo.order - bInfo.order; // Ordenar por ordem (crescente)
                                                    }

                                                    // Comparar por data de início como fallback
                                                    if (aInfo.date !== undefined && bInfo.date !== undefined) {
                                                        return aInfo.date - bInfo.date; // Ordenar por data (crescente)
                                                    }

                                                    return 0; // Manter a ordem original se não conseguir comparar
                                                })
                                            : [];

                                        if (productivityForSelectedPeriod) {
                                            return (
                                                <div className="flex flex-col">
                                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Medição realizada em</h1>
                                                    <div className="flex flex-col gap-1">
                                                        {/* Medição do período selecionado */}
                                                        <span className="text-sm font-semibold px-3 py-1.5 rounded-md bg-amber-50 text-amber-700 border border-amber-200 shadow-sm w-full text-center flex items-center justify-center gap-1 h-[40px]">
                                                            <Calendar size={16} className="text-amber-600" />
                                                            <span className="font-semibold">
                                                                {(() => {
                                                                    try {
                                                                        const date = new Date(productivityForSelectedPeriod.startDate);
                                                                        return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: 'numeric' });
                                                                    } catch (e) {
                                                                        console.error('Error formatting date:', e);
                                                                        return 'Data inválida';
                                                                    }
                                                                })()}
                                                            </span>
                                                        </span>

                                                        {/* Outras medições */}
                                                        {otherProductivities.length > 0 && (
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-1 mt-1">
                                                                {otherProductivities.map((p: any) => (
                                                                    <span key={p.id} className="text-xs px-2 py-1 rounded-md bg-gray-50 text-gray-600 border border-gray-200 shadow-sm flex items-center gap-1 w-full">
                                                                        <Calendar size={12} className="text-gray-500 flex-shrink-0" />
                                                                        <span className="font-medium whitespace-nowrap">{p.periodicity.label}:</span>
                                                                        <span className="truncate">
                                                                            {(() => {
                                                                                try {
                                                                                    const date = new Date(p.startDate);
                                                                                    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
                                                                                } catch (e) {
                                                                                    console.error('Error formatting date:', e);
                                                                                    return 'Data inválida';
                                                                                }
                                                                            })()}
                                                                        </span>
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        } else {
                                            return (
                                                <div className="flex flex-col">
                                                    <h1 className="text-sm text-gray-500 font-medium mb-1">Medição realizada em</h1>
                                                    <div className="flex flex-col gap-1">
                                                        {/* Mensagem de medição não realizada */}
                                                        <span className="text-sm font-medium px-3 py-1.5 rounded-md bg-gray-50 text-gray-500 border border-gray-200 shadow-sm w-full text-center flex items-center justify-center h-[40px]">
                                                            Medição não realizada
                                                        </span>

                                                        {/* Outras medições */}
                                                        {otherProductivities.length > 0 && (
                                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-1 mt-1">
                                                                {otherProductivities.map((p: any) => (
                                                                    <span key={p.id} className="text-xs px-2 py-1 rounded-md bg-gray-50 text-gray-600 border border-gray-200 shadow-sm flex items-center gap-1 w-full">
                                                                        <Calendar size={12} className="text-gray-500 flex-shrink-0" />
                                                                        <span className="font-medium whitespace-nowrap">{p.periodicity.label}:</span>
                                                                        <span className="truncate">
                                                                            {(() => {
                                                                                try {
                                                                                    const date = new Date(p.startDate);
                                                                                    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
                                                                                } catch (e) {
                                                                                    console.error('Error formatting date:', e);
                                                                                    return 'Data inválida';
                                                                                }
                                                                            })()}
                                                                        </span>
                                                                    </span>
                                                                ))}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                            );
                                        }
                                    })()}
                                </div>

                                {/* Coluna 6: Ações */}
                                <div className="flex flex-col lg:col-span-2">
                                    {/* Botão de ação */}
                                    <div className="flex flex-col">
                                        <h1 className="text-sm text-gray-500 font-medium mb-1">Ações</h1>
                                        <button
                                            className="w-full h-[40px] px-3 rounded-md bg-green-50 hover:bg-green-100 transition-colors duration-200 border border-green-200 shadow-sm flex items-center justify-center gap-2 whitespace-nowrap"
                                            onClick={() => {
                                                // Definir o orçamento selecionado sem acionar recarregamento da lista
                                                // Adicionar o ID do serviço ao orçamento para pré-selecionar no modal
                                                // Garantir que os valores numéricos sejam convertidos corretamente
                                                const serviceCost = Number(budget.serviceCost);
                                                const totalBudget = Number(proposal?.budget || 0);

                                                // Calcular o percentual
                                                let percentage = 0;
                                                if (totalBudget > 0) {
                                                    percentage = (serviceCost / totalBudget) * 100;
                                                    percentage = Math.round(percentage * 100) / 100; // Arredondar para 2 casas decimais
                                                }

                                                // CRITICAL FIX: Verificar se temos dados de produtividade para o período selecionado
                                                let productivityForPeriod = null;

                                                if (budget.productivity && budget.productivity.length > 0) {
                                                    // Encontrar a produtividade para o período selecionado
                                                    productivityForPeriod = budget.productivity.find(p =>
                                                        p.periodicity && p.periodicity.id === periodSelected.id
                                                    );

                                                    console.log('CRITICAL FIX: Found productivity for selected period:', productivityForPeriod);

                                                    // Se não encontrou produtividade para o período selecionado, verificar pelo conteúdo
                                                    if (!productivityForPeriod && periodSelected.id.includes('-')) {
                                                        // Extrair mês e ano do período selecionado
                                                        const [monthStr, yearStr] = periodSelected.id.split('-');
                                                        const month = parseInt(monthStr, 10); // Mês real (1-12)
                                                        const year = parseInt(yearStr, 10);

                                                        console.log(`CRITICAL FIX: Searching by content for month=${month}, year=${year}`);

                                                        // Buscar pelo conteúdo
                                                        productivityForPeriod = budget.productivity.find(p =>
                                                            p.periodicity &&
                                                            p.periodicity.content &&
                                                            p.periodicity.content.includes(`month=${month}`) &&
                                                            p.periodicity.content.includes(`year=${year}`)
                                                        );

                                                        console.log('CRITICAL FIX: Found productivity by content:', productivityForPeriod);
                                                    }
                                                }

                                                // CRITICAL FIX: Buscar dados de produtividade do novo endpoint
                                                console.log('CRITICAL FIX: Fetching productivity data from API');
                                                console.log('- proposalId:', proposal?.id || 'undefined');
                                                console.log('- repairBudgetId:', budget.id);
                                                console.log('- periodId:', periodSelected.id);

                                                // Construir a URL com os parâmetros
                                                const apiUrl = `/api/productivity?proposalId=${proposal?.id || ''}&repairBudgetId=${budget.id}&periodId=${periodSelected.id}`;
                                                console.log('CRITICAL FIX: API URL:', apiUrl);

                                                // Mostrar loading
                                                setLoading(true);

                                                // Fazer a requisição
                                                fetch(apiUrl)
                                                    .then(response => {
                                                        console.log('CRITICAL FIX: API response status:', response.status);
                                                        if (!response.ok) {
                                                            throw new Error(`API returned ${response.status}`);
                                                        }
                                                        return response.json();
                                                    })
                                                    .then(data => {
                                                        console.log('CRITICAL FIX: API response data:', data);

                                                        // Adicionar informações calculadas
                                                        const budgetWithServiceId = {
                                                            ...data,
                                                            serviceId: budget.serviceScope.id,
                                                            serviceCost: serviceCost,
                                                            totalBudget: totalBudget,
                                                            calculatedPercentage: percentage
                                                        };

                                                        console.log('CRITICAL FIX: Opening productivity modal with data:', budgetWithServiceId);

                                                        // Definir o orçamento com os dados da API
                                                        setRepairBudgetHistogram(budgetWithServiceId as any);

                                                        // Abrir o modal
                                                        setOpenProductivity(true);
                                                    })
                                                    .catch(error => {
                                                        console.error('CRITICAL FIX: Error fetching productivity data:', error);
                                                        console.error('Erro ao buscar dados de produtividade');

                                                        // Criar um objeto com os dados básicos para o modal
                                                        // Criar um objeto com os dados mínimos necessários para o tipo RepairBudget
                                                        const budgetWithServiceId = {
                                                            ...budget, // Manter todas as propriedades originais do orçamento
                                                            serviceId: budget.serviceScope.id,
                                                            serviceCost: serviceCost,
                                                            totalBudget: totalBudget,
                                                            calculatedPercentage: percentage,
                                                            productivity: productivityForPeriod ? [productivityForPeriod] : []
                                                        };

                                                        // Definir o orçamento com os dados básicos
                                                        setRepairBudgetHistogram(budgetWithServiceId as any);

                                                        // Abrir o modal mesmo assim
                                                        setOpenProductivity(true);
                                                    })
                                                    .finally(() => {
                                                        setLoading(false);
                                                    });
                                            }}
                                            title={isSamePeriod(budget.productivity, periodSelected) ? "Editar medição" : "Realizar medição"}
                                        >
                                            <Ruler className="text-green-600" size={16} />
                                            <span className="text-sm font-medium text-green-700">
                                                {isSamePeriod(budget.productivity, periodSelected) ? "Editar medição" : "Realizar medição"}
                                            </span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ))
                ) : (
                    <div className="h-40 flex items-center justify-center text-center text-gray-500 font-semibold bg-gray-50 rounded-lg border border-gray-200 shadow-sm">
                        <div className="p-6"> Não há atividades cadastradas. Clique em &quot;Adicionar Atividade&quot; para começar. </div>
                    </div>
                )}
            </div>
        </ContentWrapper >
    )
}
